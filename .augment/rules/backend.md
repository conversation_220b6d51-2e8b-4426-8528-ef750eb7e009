---
type: "always_apply"
---

- 在根目录下的 log-2.md （如果没有就创建）记录你修改过的、或者新建的 api 的接口的信息，包括输入和输出，供我前端使用；
  参考形式：

  ````
  ### 发送好友请求

  - **接口路径**: `POST /api/friends`
  - **功能**: 向指定用户发送好友请求
  - **测试性Curl：**

  ```bash
  curl -i -X POST http://*************:3000/api/friends" \
    -H "content-type: application/json" \
    -d '{"userId":"alice","friendId":"bob"}'

  ````

  - **请求参数**:
    ```typescript
    // Body 参数
    {
      userId: string; // 发起请求的用户ID
      friendId: string; // 目标用户ID
    }
    ```
  - **响应数据**:
    ```typescript
    // 成功响应
    {
      success: true;
      message: "好友请求发送成功";
      data: {
        id: string;
        userId: string;
        friendId: string;
        status: "pending";
        createdAt: Date;
        updatedAt: Date;
      }
    }
    ```
  - **状态码**:
    - `200`: 成功
    - `400`: 参数错误或关系已存在
    - `404`: 目标用户不存在
    - `500`: 服务器错误

  ```

  ```

- 确保使用 TS 进行类型规定
- 撰写 api 时，注意查看相关文件 schema.prisma，其中有相关的定义，如果必要可以修改字段
- 注意除了 GET 之外的请求体的参数全都放在 json 中，不要用?userId = x 的形式写在 url 中，保持规范；GET 使用 Query 传输参数
- 每次完成所有任务之后执行 npm run build 确保不存在编译错误
