# 奖励相关接口速查

本文档汇总用户日志与三类奖励活动（地点打卡、出行记录、卡片创建）的常用查询接口。示例均展示接口真实返回的奖励日志结构，便于前端直接使用。

## 1. 查询用户日志
- **Endpoint**: `GET /api/user-logs`
- **Query 参数**
  - `userId` *(必填)*: 目标用户 ID。
  - `recordType` *(可选)*: `location`｜`trip`｜`recognition`。
  - `isPublic` *(可选)*: `true`｜`false`。
  - `startDate` / `endDate` *(可选)*: ISO 时间字符串。
  - `page` / `limit` *(可选)*: 默认 `1` / `20`，`limit` ≤ 100。

```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "cmg2dg85w000jqge3woer4wtw",
        "recordType": "location",
        "recordId": "cmg2dg83e000hqge3jk0tjgx0",
        "createdAt": "2025-09-27T14:34:12.000Z",
        "LocationCheckIns": {
          "id": "cmg2dg83e000hqge3jk0tjgx0",
          "position": "浙江大学玉泉校区学生宿舍14舍-2",
          "rewardAttributeLog": {
            "id": "cmg2dg8bv000lqge3ouh1spco",
            "userId": "ffy6511",
            "carbonCoinsDelta": 0,
            "experiencePointsDelta": 5,
            "carbonReductionDelta": 0,
            "honorPointsDelta": 0,
            "reason": "location_check_in",
            "metadata": {
              "position": "浙江大学玉泉校区学生宿舍14舍-2",
              "userLogId": "cmg2dg85w000jqge3woer4wtw",
              "locationCheckInId": "cmg2dg83e000hqge3jk0tjgx0"
            },
            "createdAt": "2025-09-27T14:34:12.000Z"
          }
        }
      },
      {
        "id": "cmg2dgz0x000oqge37jq5ip84",
        "recordType": "trip",
        "recordId": "cmg2dgwye000mqge3jtalftjc",
        "UserFootprints": {
          "id": "cmg2dgwye000mqge3jtalftjc",
          "totalDistance": 3.2,
          "completionRewardLog": {
            "id": "cmg2dgz3p000pqge3jjbg0u0k",
            "userId": "ffy6511",
            "carbonCoinsDelta": 16,
            "experiencePointsDelta": 16,
            "carbonReductionDelta": 1.6,
            "honorPointsDelta": 0,
            "reason": "footprint_completed",
            "metadata": {
              "activityType": "cycling",
              "distance": 3.2,
              "footprintId": "cmg2dgwye000mqge3jtalftjc",
              "userLogId": "cmg2dgz0x000oqge37jq5ip84"
            },
            "createdAt": "2025-09-27T15:02:11.000Z"
          }
        }
      },
      {
        "id": "cmg2dglon000mqge31badp4lw",
        "recordType": "recognition",
        "recordId": "cmg2dgl2x000kqge3tufh22hf",
        "ItemCard": {
          "id": "cmg2dgl2x000kqge3tufh22hf",
          "title": "环保出行",
          "creationRewardLog": {
            "id": "cmg2dglrn000nqge3sk03z5qv",
            "userId": "ffy6511",
            "carbonCoinsDelta": 10,
            "experiencePointsDelta": 20,
            "carbonReductionDelta": 0,
            "honorPointsDelta": 0,
            "reason": "item_card_created",
            "metadata": {
              "cardId": "cmg2dgl2x000kqge3tufh22hf",
              "reward": {
                "carbonCoins": 10,
                "experiencePoints": 20
              },
              "userLogId": "cmg2dglon000mqge31badp4lw"
            },
            "createdAt": "2025-09-27T13:58:40.000Z"
          }
        }
      }
    ],
    "pagination": {
      "current": 1,
      "total": 3,
      "count": 42,
      "limit": 20,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

> 日志会自动展开对应的业务记录与奖励：
> - `LocationCheckIns.rewardAttributeLog`
> - `UserFootprints.completionRewardLog`
> - `ItemCard.creationRewardLog`

## 2. 查询地点打卡
- **Endpoint**: `GET /api/location-checkins`
- **Query 参数**
  - `userId` *(必填)*
  - `startDate` / `endDate` *(可选)*

```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": "cmg2dg83e000hqge3jk0tjgx0",
      "position": "浙江大学玉泉校区学生宿舍14舍-2",
      "createdAt": "2025-09-27T14:34:12.000Z",
      "rewardAttributeLog": {
        "id": "cmg2dg8bv000lqge3ouh1spco",
        "userId": "ffy6511",
        "carbonCoinsDelta": 0,
        "experiencePointsDelta": 5,
        "carbonReductionDelta": 0,
        "honorPointsDelta": 0,
        "reason": "location_check_in",
        "metadata": {
          "position": "浙江大学玉泉校区学生宿舍14舍-2",
          "userLogId": "cmg2dg85w000jqge3woer4wtw",
          "locationCheckInId": "cmg2dg83e000hqge3jk0tjgx0"
        },
        "createdAt": "2025-09-27T14:34:12.000Z"
      }
    }
  ]
}
```

## 3. 查询出行记录（足迹）
- **Endpoint**: `GET /api/footprints`
- **Query 参数**
  - `userId` *(必填)*
  - `activityType` *(可选)*: `walking`｜`cycling`｜`bus`｜`subway`
  - `startDate` / `endDate` *(可选)*

```json
{
  "success": true,
  "data": [
    {
      "id": "cmg2dgwye000mqge3jtalftjc",
      "activityType": "cycling",
      "totalDistance": 3.2,
      "isFinished": true,
      "completionRewardLog": {
        "id": "cmg2dgz3p000pqge3jjbg0u0k",
        "userId": "ffy6511",
        "carbonCoinsDelta": 16,
        "experiencePointsDelta": 16,
        "carbonReductionDelta": 1.6,
        "honorPointsDelta": 0,
        "reason": "footprint_completed",
        "metadata": {
          "activityType": "cycling",
          "distance": 3.2,
          "footprintId": "cmg2dgwye000mqge3jtalftjc",
          "userLogId": "cmg2dgz0x000oqge37jq5ip84"
        },
        "createdAt": "2025-09-27T15:02:11.000Z"
      }
    }
  ]
}
```

---
如需更多字段，可参考相应 route 中的 Prisma `include` 配置。
