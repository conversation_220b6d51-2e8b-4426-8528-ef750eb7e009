import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import bcrypt from "bcryptjs"; // 计算输入密码对应的哈希值

// 用户注册
export async function POST(req: Request) {
  const { userId, password } = await req.json();

  // 检查用户是否已存在
  const existingUser = await prisma.user.findUnique({
    where: { userId },
  });

  if (existingUser) {
    return NextResponse.json({ error: "ID已存在" }, { status: 400 });
  }

  // 哈希密码
  const passwordHash = await bcrypt.hash(password, 10);

  // 生成一个随机名称
  const nickname = `用户${Math.floor(Math.random() * 10000)}`;

  // 创建新用户
  const user = await prisma.user.create({
    data: {
      userId,
      passwordHash,
      nickname,
    },
  });

  return NextResponse.json({ user, message: "注册成功", userId: user.userId });
}
