import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 获取用户的好友列表
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 查询用户发起的好友关系
    const sentFriendships = await prisma.friendship.findMany({
      where: { userId },
      include: {
        user: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
      },
    });

    // 查询用户收到的好友关系（别人发给该用户的请求）
    const receivedFriendships = await prisma.friendship.findMany({
      where: { friendId: userId },
      include: {
        user: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
      },
    });

    // 获取好友的详细信息
    const friendsWithDetails = await Promise.all([
      // 处理用户发起的好友关系
      ...sentFriendships.map(async (friendship) => {
        const friendInfo = await prisma.user.findUnique({
          where: { userId: friendship.friendId },
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        });

        return {
          friendshipId: friendship.id,
          friend: friendInfo,
          status: friendship.status,
          createdAt: friendship.createdAt,
          updatedAt: friendship.updatedAt,
          type: "sent" as const, // 用户发起的请求
        };
      }),
      // 处理用户收到的好友关系
      ...receivedFriendships.map(async (friendship) => ({
        friendshipId: friendship.id,
        friend: {
          userId: friendship.user.userId,
          nickname: friendship.user.nickname,
          avatarURL: friendship.user.avatarURL,
        },
        status: friendship.status,
        createdAt: friendship.createdAt,
        updatedAt: friendship.updatedAt,
        type: "received" as const, // 用户收到的请求
      })),
    ]);

    // 按状态分类
    const friends = {
      accepted: friendsWithDetails.filter((f) => f.status === "accepted"),
      pending: friendsWithDetails.filter((f) => f.status === "pending"),
      rejected: friendsWithDetails.filter((f) => f.status === "rejected"),
    };

    return NextResponse.json({
      success: true,
      data: friends,
    });
  } catch (error) {
    console.error("获取好友列表失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 发送好友请求
export async function POST(req: Request) {
  try {
    const { userId, friendId } = await req.json();

    if (!userId || !friendId) {
      return NextResponse.json(
        { error: "缺少userId或friendId参数" },
        { status: 400 }
      );
    }

    if (userId === friendId) {
      return NextResponse.json(
        { error: "不能添加自己为好友" },
        { status: 400 }
      );
    }

    // 检查目标用户是否存在
    const targetUser = await prisma.user.findUnique({
      where: { userId: friendId },
    });

    if (!targetUser) {
      return NextResponse.json({ error: "目标用户不存在" }, { status: 404 });
    }

    // 检查是否已存在好友关系（双向检查），并不包括rejected
    const existingFriendship = await prisma.friendship.findFirst({
      where: {
        OR: [
          { userId, friendId },
          { userId: friendId, friendId: userId },
        ],
        status: { not: "rejected" },
      },
    });

    if (existingFriendship) {
      return NextResponse.json(
        { error: "好友关系已存在或请求已发送" },
        { status: 400 }
      );
    }

    // 创建好友请求
    const friendship = await prisma.friendship.create({
      data: {
        userId,
        friendId,
        status: "pending",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: "好友请求发送成功",
      data: friendship,
    });
  } catch (error) {
    console.error("发送好友请求失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 处理好友请求（同意/拒绝）
export async function PUT(req: Request) {
  try {
    const { userId, friendId, action } = await req.json();

    if (!userId || !friendId || !action) {
      return NextResponse.json({ error: "缺少必要参数" }, { status: 400 });
    }

    if (!["accept", "reject"].includes(action)) {
      return NextResponse.json({ error: "无效的操作类型" }, { status: 400 });
    }

    // 查找待处理的好友请求
    const friendship = await prisma.friendship.findFirst({
      where: {
        userId: friendId, // 收到好友请求的用户
        friendId: userId, // 发送请求的用户
        status: "pending",
      },
    });

    if (!friendship) {
      return NextResponse.json(
        { error: "未找到待处理的好友请求" },
        { status: 404 }
      );
    }

    // 更新好友请求状态
    const updatedFriendship = await prisma.friendship.update({
      where: { id: friendship.id },
      data: {
        status: action === "accept" ? "accepted" : "rejected",
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: action === "accept" ? "已同意好友请求" : "已拒绝好友请求",
      data: updatedFriendship,
    });
  } catch (error) {
    console.error("处理好友请求失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 删除好友关系
export async function DELETE(req: Request) {
  try {
    const { userId, friendId } = await req.json();

    if (!userId || !friendId) {
      return NextResponse.json(
        { error: "缺少userId或friendId参数" },
        { status: 400 }
      );
    }

    // 查找好友关系（双向查找）
    const friendship = await prisma.friendship.findFirst({
      where: {
        OR: [
          { userId, friendId, status: "accepted" },
          { userId: friendId, friendId: userId, status: "accepted" },
        ],
      },
    });

    if (!friendship) {
      return NextResponse.json({ error: "未找到好友关系" }, { status: 404 });
    }

    // 删除好友关系
    await prisma.friendship.delete({
      where: { id: friendship.id },
    });

    return NextResponse.json({
      success: true,
      message: "好友关系已删除",
    });
  } catch (error) {
    console.error("删除好友关系失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
