import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { applyUserStatChanges } from "@/lib/userStats";

// 创建新的卡片
export async function POST(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const {
      cardType,
      themeColor,
      coinReward = 0,
      experienceReward = 0,
      description,
      title,
      imageFileName,
      imageURL,
      location,
      latitude,
      longitude,
    } = await req.json();

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    if (!cardType || !["scenery", "shopping"].includes(cardType)) {
      return NextResponse.json(
        { error: "无效的卡片类型，必须是 scenery 或 shopping" },
        { status: 400 }
      );
    }

    // 使用事务创建卡片、获得记录和日志记录
    const result = await prisma.$transaction(async (tx) => {
      // 创建卡片
      const itemCard = await tx.itemCard.create({
        data: {
          cardType,
          themeColor: cardType === "shopping" ? themeColor : null, // 只有购物卡片才有主题色
          coinReward,
          experienceReward,
          description,
          title,
          imageFileName,
          imageURL,
          location,
          latitude,
          longitude,
          authorId: userId,
        },
      });

      // 创建卡片获得记录（作者自动获得）
      await tx.cardAcquisitionRecord.create({
        data: {
          userId: userId,
          cardId: itemCard.id,
          isAuthor: true,
        },
      });

      // 自动创建日志记录
      const userLog = await tx.userLogs.create({
        data: {
          userId: userId,
          recordType: "recognition",
          recordId: itemCard.id,
          imageList: [], // 不使用卡片的图片作为日志图片
          description: `创建了新的${
            cardType === "scenery" ? "风景" : "购物"
          }卡片：${title}`,
          isPublic: false,
          itemCardId: itemCard.id, // 关联到ItemCard
        },
      });

      // 给用户发放奖励
      const statChanges = {
        carbonCoins: coinReward > 0 ? coinReward : 0,
        experiencePoints: experienceReward > 0 ? experienceReward : 0,
      };

      let rewardLogId: string | null = null;
      if (statChanges.carbonCoins || statChanges.experiencePoints) {
        const rewardResult = await applyUserStatChanges(tx, {
          userId,
          changes: statChanges,
          reason: "item_card_created",
          metadata: {
            cardId: itemCard.id,
            reward: {
              carbonCoins: coinReward,
              experiencePoints: experienceReward,
            },
            userLogId: userLog.id,
          },
        });

        rewardLogId = rewardResult.log.id;
      }

      if (rewardLogId) {
        await tx.itemCard.update({
          where: { id: itemCard.id },
          data: {
            creationRewardLogId: rewardLogId,
          },
        });
      }

      const finalCard = await tx.itemCard.findUnique({
        where: { id: itemCard.id },
        include: {
          creationRewardLog: true,
        },
      });

      return finalCard!;
    });

    return NextResponse.json({
      success: true,
      message: "卡片创建成功",
      data: result,
    });
  } catch (error) {
    console.error("创建卡片失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 }
    );
  }
}

// 修改卡片信息（只有作者可以修改）
export async function PATCH(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const cardId = searchParams.get("cardId");

    if (!userId || !cardId) {
      return NextResponse.json(
        { error: "缺少userId或cardId参数" },
        { status: 400 }
      );
    }

    const {
      cardType,
      themeColor,
      coinReward,
      experienceReward,
      description,
      title,
      imageFileName,
      imageURL,
      location,
      latitude,
      longitude,
    } = await req.json();

    // 验证卡片存在且用户是作者
    const existingCard = await prisma.itemCard.findUnique({
      where: { id: cardId },
    });

    if (!existingCard) {
      return NextResponse.json({ error: "卡片不存在" }, { status: 404 });
    }

    if (existingCard.authorId !== userId) {
      return NextResponse.json(
        { error: "只有作者可以修改卡片信息" },
        { status: 403 }
      );
    }

    // 验证卡片类型（如果要修改的话）
    if (cardType !== undefined && !["scenery", "shopping"].includes(cardType)) {
      return NextResponse.json(
        { error: "无效的卡片类型，必须是 scenery 或 shopping" },
        { status: 400 }
      );
    }

    // 更新卡片信息
    const updatedCard = await prisma.itemCard.update({
      where: { id: cardId },
      data: {
        ...(cardType !== undefined && { cardType }),
        ...(themeColor !== undefined && { themeColor }),
        ...(coinReward !== undefined && { coinReward }),
        ...(experienceReward !== undefined && { experienceReward }),
        ...(description !== undefined && { description }),
        ...(title !== undefined && { title }),
        ...(imageFileName !== undefined && { imageFileName }),
        ...(imageURL !== undefined && { imageURL }),
        ...(location !== undefined && { location }),
        ...(latitude !== undefined && { latitude }),
        ...(longitude !== undefined && { longitude }),
      },
    });

    return NextResponse.json({
      success: true,
      message: "卡片信息更新成功",
      data: updatedCard,
    });
  } catch (error) {
    console.error("更新卡片失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 }
    );
  }
}
