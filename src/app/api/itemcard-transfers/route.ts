import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 创建卡片传输请求
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { senderId, receiverId, cardId } = body;

    // 验证输入参数
    if (!senderId || !receiverId || !cardId) {
      return NextResponse.json(
        { error: "缺少必要参数：senderId, receiverId, cardId" },
        { status: 400 }
      );
    }

    if (senderId === receiverId) {
      return NextResponse.json(
        { error: "不能向自己传输卡片" },
        { status: 400 }
      );
    }

    // 验证发送者存在
    const sender = await prisma.user.findUnique({
      where: { userId: senderId },
    });

    if (!sender) {
      return NextResponse.json({ error: "发送者不存在" }, { status: 404 });
    }

    // 验证接收者存在
    const receiver = await prisma.user.findUnique({
      where: { userId: receiverId },
    });

    if (!receiver) {
      return NextResponse.json({ error: "接收者不存在" }, { status: 404 });
    }

    // 验证卡片存在
    const card = await prisma.itemCard.findUnique({
      where: { id: cardId },
    });

    if (!card) {
      return NextResponse.json({ error: "卡片不存在" }, { status: 404 });
    }

    // 检查是否已存在待处理的传输请求
    const existingTransfer = await prisma.itemCardTransfer.findFirst({
      where: {
        senderId,
        receiverId,
        cardId,
        status: "pending",
      },
    });

    if (existingTransfer) {
      return NextResponse.json(
        { error: "已存在待处理的传输请求" },
        { status: 400 }
      );
    }

    // 创建传输请求
    const transfer = await prisma.itemCardTransfer.create({
      data: {
        senderId,
        receiverId,
        cardId,
        status: "pending",
      },
      // 获取相关联的数据
      include: {
        sender: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
        receiver: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
        card: {
          select: {
            id: true,
            title: true,
            description: true,
            imageURL: true,
            cardType: true,
            themeColor: true,
            coinReward: true,
            experienceReward: true,
            location: true,
            createdAt: true,
            authorId: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "卡片传输请求创建成功",
      data: transfer,
    });
  } catch (error) {
    console.error("创建卡片传输请求失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 处理传输请求（接受或拒绝）
export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();
    const { transferId, action, userId } = body;

    // 验证输入参数
    if (!transferId || !action || !userId) {
      return NextResponse.json(
        { error: "缺少必要参数：transferId, action, userId" },
        { status: 400 }
      );
    }

    if (!["accept", "reject"].includes(action)) {
      return NextResponse.json(
        { error: "无效的操作类型，只支持 accept 或 reject" },
        { status: 400 }
      );
    }

    // 查找传输请求
    const transfer = await prisma.itemCardTransfer.findUnique({
      where: { id: transferId },
      include: {
        sender: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
        receiver: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
        card: true,
      },
    });

    if (!transfer) {
      return NextResponse.json({ error: "传输请求不存在" }, { status: 404 });
    }

    // 验证只有接收者可以处理请求
    if (transfer.receiverId !== userId) {
      return NextResponse.json(
        { error: "只有接收者可以处理传输请求" },
        { status: 403 }
      );
    }

    // 验证请求状态
    if (transfer.status !== "pending") {
      return NextResponse.json(
        { error: "该传输请求已被处理" },
        { status: 400 }
      );
    }

    let updatedTransfer;

    if (action === "accept") {
      // 接受传输：更新传输状态并创建用户持有记录
      updatedTransfer = await prisma.$transaction(async (tx) => {
        // 更新传输状态
        const transfer = await tx.itemCardTransfer.update({
          where: { id: transferId },
          data: {
            status: "accepted",
          },
          include: {
            sender: {
              select: {
                userId: true,
                nickname: true,
                avatarURL: true,
              },
            },
            receiver: {
              select: {
                userId: true,
                nickname: true,
                avatarURL: true,
              },
            },
            card: {
              select: {
                id: true,
                title: true,
                description: true,
                imageURL: true,
                cardType: true,
                themeColor: true,
                coinReward: true,
                experienceReward: true,
                authorId: true, // 保持原始作者信息
                location: true,
                createdAt: true,
              },
            },
          },
        });

        // 创建接收者的卡片获得记录
        await tx.cardAcquisitionRecord.create({
          data: {
            userId: transfer.receiverId,
            cardId: transfer.cardId,
            isAuthor: false, // 接收者不是作者
          },
        });

        return transfer;
      });
    } else {
      // 拒绝传输：只更新传输状态
      updatedTransfer = await prisma.itemCardTransfer.update({
        where: { id: transferId },
        data: {
          status: "rejected",
        },
        include: {
          sender: {
            select: {
              userId: true,
              nickname: true,
              avatarURL: true,
            },
          },
          receiver: {
            select: {
              userId: true,
              nickname: true,
              avatarURL: true,
            },
          },
          card: {
            select: {
              id: true,
              title: true,
              description: true,
              imageURL: true,
              cardType: true,
              themeColor: true,
              coinReward: true,
              experienceReward: true,
              authorId: true, // 保持原始作者信息
              location: true,
              createdAt: true,
            },
          },
        },
      });
    }

    return NextResponse.json({
      success: true,
      message:
        action === "accept" ? "卡片传输已接受，卡片已转移" : "卡片传输已拒绝",
      data: updatedTransfer,
    });
  } catch (error) {
    console.error("处理传输请求失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
