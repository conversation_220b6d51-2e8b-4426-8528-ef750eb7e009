import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 查询用户发送的传输记录
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const status = searchParams.get("status"); // 可选：过滤特定状态

    if (!userId) {
      return NextResponse.json({ error: "缺少 userId 参数" }, { status: 400 });
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 构建查询条件
    const whereCondition: any = {
      senderId: userId,
    };

    // 如果指定了状态，添加状态过滤
    if (status && ["pending", "accepted", "rejected"].includes(status)) {
      whereCondition.status = status;
    }

    // 查询发送的传输记录
    const transfers = await prisma.itemCardTransfer.findMany({
      where: whereCondition,
      include: {
        receiver: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
        card: {
          select: {
            id: true,
            title: true,
            description: true,
            imageURL: true,
            cardType: true,
            themeColor: true,
            structureScore: true,
            materialScore: true,
            packagingScore: true,
            mark: true,
            location: true,
            createdAt: true,
          },
        },
      },
      orderBy: {
        transferTime: "desc", // 按传输时间倒序排列
      },
    });

    // 按状态分类
    const categorizedTransfers = {
      pending: transfers.filter((t: any) => t.status === "pending"),
      accepted: transfers.filter((t: any) => t.status === "accepted"),
      rejected: transfers.filter((t: any) => t.status === "rejected"),
    };

    return NextResponse.json({
      success: true,
      data: {
        transfers: status ? transfers : categorizedTransfers,
        total: transfers.length,
      },
    });
  } catch (error) {
    console.error("查询发送传输记录失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
