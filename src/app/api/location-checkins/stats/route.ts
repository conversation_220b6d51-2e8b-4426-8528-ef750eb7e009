import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 统计查询参数类型定义
interface StatsParams {
  userId: string;
  startDate?: string;
  endDate?: string;
}

// 地点统计类型定义
interface LocationStats {
  position: string;
  count: number;
}

// 查询地点打卡统计
export async function POST(req: NextRequest) {
  try {
    const { userId, startDate, endDate }: StatsParams = await req.json();

    // 参数验证
    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证用户是否存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 构建查询条件
    const whereCondition: any = {
      userId,
    };

    // 添加时间范围过滤
    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt.lte = new Date(endDate);
      }
    }

    // 查询所有符合条件的打卡记录
    const checkIns = await prisma.locationCheckIns.findMany({
      where: whereCondition,
      select: {
        id: true,
        position: true,
        createdAt: true,
      },
    });

    // 计算总打卡次数
    const totalCheckIns = checkIns.length;

    // 统计按地点分组的打卡次数
    const positionStats: { [key: string]: number } = {};

    checkIns.forEach((checkIn) => {
      const position = checkIn.position || "未知地点";
      positionStats[position] = (positionStats[position] || 0) + 1;
    });

    // 转换为数组并按次数从多到少排序
    const sortedPositions: LocationStats[] = Object.entries(positionStats)
      .map(([position, count]) => ({ position, count }))
      .sort((a, b) => b.count - a.count);

    // 计算不同地点的个数
    const uniquePositionsCount = Object.keys(positionStats).length;

    return NextResponse.json({
      success: true,
      message: "统计查询成功",
      data: {
        totalCheckIns,
        uniquePositionsCount,
        positionStats: sortedPositions,
        timeRange: {
          startDate: startDate || null,
          endDate: endDate || null,
        },
      },
    });
  } catch (error) {
    console.error("查询地点打卡统计失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
