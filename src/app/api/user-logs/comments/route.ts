import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 评论数据类型定义
interface CommentData {
  logId: string;
  userId: string;
  content: string;
  replyTo?: string; // 回复的评论ID，可选
}

// 创建评论
export async function POST(req: NextRequest) {
  try {
    const { logId, userId, content, replyTo }: CommentData = await req.json();

    // 参数验证
    if (!logId) {
      return NextResponse.json({ error: "缺少logId参数" }, { status: 400 });
    }

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    if (!content || content.trim().length === 0) {
      return NextResponse.json({ error: "评论内容不能为空" }, { status: 400 });
    }

    if (content.length > 500) {
      return NextResponse.json(
        { error: "评论内容不能超过500字符" },
        { status: 400 }
      );
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 验证日志记录存在
    const userLog = await prisma.userLogs.findUnique({
      where: { id: logId },
    });

    if (!userLog) {
      return NextResponse.json({ error: "日志记录不存在" }, { status: 404 });
    }

    // 如果是回复评论，验证被回复的评论存在且属于同一日志
    if (replyTo) {
      const parentComment = await prisma.recordComments.findUnique({
        where: { id: replyTo },
      });

      if (!parentComment) {
        return NextResponse.json(
          { error: "被回复的评论不存在" },
          { status: 404 }
        );
      }

      if (parentComment.logId !== logId) {
        return NextResponse.json(
          { error: "被回复的评论不属于当前日志" },
          { status: 400 }
        );
      }
    }

    // 使用事务创建评论记录并更新日志的评论数组
    const result = await prisma.$transaction(async (tx) => {
      // 创建评论记录
      const comment = await tx.recordComments.create({
        data: {
          logId,
          userId,
          content: content.trim(),
          replyTo: replyTo || null,
        },
        include: {
          user: {
            select: {
              userId: true,
              nickname: true,
              avatarURL: true,
            },
          },
        },
      });

      // 获取当前评论列表
      const currentLog = await tx.userLogs.findUnique({
        where: { id: logId },
        select: { commentsId: true },
      });

      // 更新日志的评论ID数组
      const currentCommentsId = Array.isArray(currentLog?.commentsId)
        ? (currentLog.commentsId as string[])
        : [];

      const updatedCommentsId = [...currentCommentsId, comment.id];

      await tx.userLogs.update({
        where: { id: logId },
        data: {
          commentsId: updatedCommentsId,
        },
      });

      return comment;
    });

    return NextResponse.json(
      {
        success: true,
        message: "评论创建成功",
        data: result,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("创建评论失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 删除评论
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const commentId = searchParams.get("commentId");
    const userId = searchParams.get("userId");

    // 参数验证
    if (!commentId) {
      return NextResponse.json({ error: "缺少commentId参数" }, { status: 400 });
    }

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证评论记录存在且属于该用户
    const existingComment = await prisma.recordComments.findUnique({
      where: { id: commentId },
    });

    if (!existingComment) {
      return NextResponse.json({ error: "评论记录不存在" }, { status: 404 });
    }

    if (existingComment.userId !== userId) {
      return NextResponse.json({ error: "无权限删除此评论" }, { status: 403 });
    }

    // 使用事务删除评论记录并更新日志的评论数组
    await prisma.$transaction(async (tx) => {
      // 删除评论记录
      await tx.recordComments.delete({
        where: { id: commentId },
      });

      // 获取当前评论列表
      const currentLog = await tx.userLogs.findUnique({
        where: { id: existingComment.logId },
        select: { commentsId: true },
      });

      // 更新日志的评论ID数组（移除当前评论ID）
      const currentCommentsId = Array.isArray(currentLog?.commentsId)
        ? (currentLog.commentsId as string[])
        : [];

      const updatedCommentsId = currentCommentsId.filter(
        (id) => id !== commentId
      );

      await tx.userLogs.update({
        where: { id: existingComment.logId },
        data: {
          commentsId: updatedCommentsId,
        },
      });
    });

    return NextResponse.json({
      success: true,
      message: "评论删除成功",
    });
  } catch (error) {
    console.error("删除评论失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
