import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 根据userId获取用户基本信息
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 查找用户基本信息
    const user = await prisma.user.findUnique({
      where: { userId },
      select: {
        userId: true,
        nickname: true,
        avatarURL: true,
        backgroundURL: true,
        carbonCoins: true,
        experiencePoints: true,
        honorPoints: true,
        carbonReduction: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error("获取用户信息失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 修改部分的用户信息
type Body = {
  userId: string;
  avatarURL?: string;
  nickname?: string;
  lastActiveTime?: string;
  sharingLocation?: boolean;
  backgroundURL?: string;
};

export async function PATCH(request: Request) {
  const body: Body = await request.json();

  const { userId } = body;
  if (!userId) {
    return NextResponse.json({ error: "缺少 userId 参数" }, { status: 400 });
  }

  const data: any = {
    avatarURL: body.avatarURL ?? undefined,
    nickname: body.nickname ?? undefined,
    lastActiveTime: new Date(),
    sharingLocation: body.sharingLocation ?? undefined,
    backgroundURL: body.backgroundURL ?? undefined,
  };

  try {
    const updated = await prisma.user.update({
      where: { userId },
      data,
      select: {
        userId: true,
        avatarURL: true,
        nickname: true,
        lastActiveTime: true,
        sharingLocation: true,
        backgroundURL: true,
        carbonCoins: true,
        experiencePoints: true,
        honorPoints: true,
        carbonReduction: true,
      },
    });

    return NextResponse.json({ success: true, data: updated });
  } catch (err) {
    console.error("更新失败", err);
    return NextResponse.json({ error: "服务器错误" }, { status: 500 });
  }
}
