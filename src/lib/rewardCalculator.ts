/**
 * 卡片奖励计算工具函数
 * 根据评分计算碳币和经验奖励
 */

export interface CardScores {
  structureScore?: number | null; // 结构评分
  materialScore?: number | null;  // 材料评分
  packagingScore?: number | null; // 包装评分
  mark?: string | null;           // 备注标记
}

export interface RewardResult {
  carbonCoins: number;    // 计算得出的碳币奖励
  experience: number;     // 计算得出的经验奖励
}

/**
 * 计算卡片创建奖励
 * 
 * 碳币计算公式：
 * carbonCoins = Math.round((structureScore * 0.3 + materialScore * 0.4 + packagingScore * 0.3) * 0.1) + (mark ? 5 : 0)
 * - 结构评分权重：30%
 * - 材料评分权重：40%
 * - 包装评分权重：30%
 * - 加权和乘以 0.1 后四舍五入
 * - 如果 mark 字段非空（非 null 且非空字符串），额外奖励 5 碳币
 * 
 * 经验计算公式：
 * experience = Math.round((structureScore + materialScore + packagingScore) * 0.2)
 * - 三个评分相加后乘以 0.2，然后四舍五入
 * 
 * @param scores 卡片评分数据
 * @returns 计算得出的奖励结果
 */
export function calculateCardReward(scores: CardScores): RewardResult {
  // 获取评分，如果为 null 或 undefined 则默认为 0
  const structureScore = scores.structureScore ?? 0;
  const materialScore = scores.materialScore ?? 0;
  const packagingScore = scores.packagingScore ?? 0;
  const mark = scores.mark;

  // 计算碳币奖励
  const weightedScore = structureScore * 0.3 + materialScore * 0.4 + packagingScore * 0.3;
  const baseCarbonCoins = Math.round(weightedScore * 0.1);
  
  // 如果 mark 字段非空（非 null 且非空字符串），额外奖励 5 碳币
  const markBonus = (mark && mark.trim() !== '') ? 5 : 0;
  const carbonCoins = baseCarbonCoins + markBonus;

  // 计算经验奖励
  const totalScore = structureScore + materialScore + packagingScore;
  const experience = Math.round(totalScore * 0.2);

  return {
    carbonCoins: Math.max(0, carbonCoins), // 确保不为负数
    experience: Math.max(0, experience),   // 确保不为负数
  };
}

/**
 * 验证评分数据的有效性
 * @param scores 评分数据
 * @returns 验证结果和错误信息
 */
export function validateCardScores(scores: CardScores): { isValid: boolean; error?: string } {
  const { structureScore, materialScore, packagingScore } = scores;

  // 检查评分范围（如果提供的话）
  const scoreFields = [
    { name: 'structureScore', value: structureScore },
    { name: 'materialScore', value: materialScore },
    { name: 'packagingScore', value: packagingScore },
  ];

  for (const field of scoreFields) {
    if (field.value !== null && field.value !== undefined) {
      if (typeof field.value !== 'number' || field.value < 0 || field.value > 100) {
        return {
          isValid: false,
          error: `${field.name} 必须是 0-100 之间的数字`,
        };
      }
    }
  }

  return { isValid: true };
}

/**
 * 格式化奖励结果用于日志记录
 * @param scores 原始评分数据
 * @param reward 计算得出的奖励
 * @returns 格式化的元数据对象
 */
export function formatRewardMetadata(scores: CardScores, reward: RewardResult) {
  return {
    scores: {
      structureScore: scores.structureScore,
      materialScore: scores.materialScore,
      packagingScore: scores.packagingScore,
      mark: scores.mark,
    },
    calculatedReward: {
      carbonCoins: reward.carbonCoins,
      experience: reward.experience,
    },
    calculation: {
      weightedScore: (scores.structureScore ?? 0) * 0.3 + (scores.materialScore ?? 0) * 0.4 + (scores.packagingScore ?? 0) * 0.3,
      totalScore: (scores.structureScore ?? 0) + (scores.materialScore ?? 0) + (scores.packagingScore ?? 0),
      markBonus: (scores.mark && scores.mark.trim() !== '') ? 5 : 0,
    },
  };
}
