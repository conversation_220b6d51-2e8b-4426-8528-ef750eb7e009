数值系统

用于行为记录外显的通用指标为三个：碳币奖励值，经验奖励值，减碳克数

根据行为本身不同，其他记录外显数据又有所不同：

出行：时长，里程数，平均速度

消费：绿色打分，商品名称

问答：已累计天数

| 模块          | 用户行为                       | 奖励机制                            | 消耗机制                       | 周期体验                               |
| ------------- | ------------------------------ | ----------------------------------- | ------------------------------ | -------------------------------------- |
| 日常行为      | 骑行/步行（1km）               | 2–10 碳币 + 10 EXP + 0.2kg CO₂ 减排 | —                              | 即时获得感，鼓励每天打卡               |
|               | 地图打卡                       | 5 EXP （每天上限 5 次）             |                                |                                        |
|               | 无塑消费/扫码打卡              | 10–20 碳币 + 50EXP + 0.5kg CO₂ 减排 | —                              | 行为转化为货币，建立价值感             |
|               | 公共交通出行（一次）           | 5–10 碳币 + 10-20 EXP               | —                              | 日常生活也能获得激励                   |
|               | 低碳生活问答（一次）           | 5–10 碳币 + 10-20 EXP               |                                | 记录生活习惯的对话，让用户形成情感依赖 |
| 任务与挑战    | 日/周任务（完成 3 次低碳出行） | 50–100 碳币 + 成就徽章              | —                              | 周期目标，增强习惯养成                 |
|               | 公益挑战（如环保活动）         | 100–300 碳币 + 稀有称号             | —                              | 强化社交与仪式感                       |
| 奖励货币      | 累积减碳量（每 1kg CO₂）       | ≈50 碳币 + 荣誉值                   | —                              | 长期量化，直观展示贡献                 |
| 虚拟消费      | 装扮/精灵养成                  | —                                   | 100–2000 碳币                  | 每周满足，社交驱动                     |
|               | 地图标识探索/新的碳币精灵解锁  | —                                   | 500–3000 碳币                  | 中期目标，提升留存                     |
| 实物/价值消费 | 兑换实物/优惠券                | —                                   | 2000–10000 碳币                | 月度目标，驱动长期积累                 |
|               | 公益捐赠/植树                  | —                                   | 500–2000 碳币                  | 精神满足与荣誉激励                     |
| 社交循环      | 排行榜/称号展示                | —                                   | 根据等级，公益挑战和荣誉值计算 | 形成竞争/炫耀动机                      |

### 出行奖励计算方式：

任务：

根据出行方式和距离计算用户获得的碳币奖励

背景：

标准车排量为 3.0T 的轿车每公里排放 220 克二氧化碳。根据用户的出行行为，计算其减少的碳量，并根据每千克二氧化碳对应 10 碳币的比例，给出相应的碳币奖励。调整后，每次出行的碳币奖励大约在 2 到 10 之间。

输入：

1. 用户的出行方式（如步行、骑行、公共交通）。
2. 用户的出行距离（以公里为单位）。
3. 标准车排量为 3.0T，每公里排放 220 克 CO₂。

输出：

1. 每种出行方式的碳减排量。
2. 每种出行方式对应的碳币奖励。

减碳量与奖励关系：

- 步行：每公里约减少 220 克 CO₂，每 1000 克减碳量奖励 10 碳币。
- 骑行：每公里约减少 200 克 CO₂，每 1000 克减碳量奖励 10 碳币。
- 公共交通：每公里约减少 150 克 CO₂，每 1000 克减碳量奖励 10 碳币。

计算方式：

- 奖励碳币数值 =公里数 × 每公里减碳量 × 每千克减碳量 CO₂ 奖励（10 碳币）
- 奖励经验值 = 公里数 × 每公里经验值奖励（5 EXP），（公共交通与驾车出行按时长计算，每小时 20EXP，计算公式为奖励经验值 = 小时数 × 每小时经验值奖励（20 EXP））

### 消费奖励计算方式：

任务：根据消费商品的绿色属性计算用户获得的碳币奖励

背景：

根据用户购买商品的视觉识别（如商品是否本地生产、是否使用环保材料、是否为无塑包装等），计算相应的碳币奖励。每次消费奖励的碳币数值需在 2 到 10 之间，根据商品的绿色属性进行调整。

输入：

1. 用户购买商品的类别（如食品、家电、衣物等）。
2. 用户购买商品的绿色属性（如是否本地生产、是否使用环保材料、是否无塑包装等）。
3. 用户购买商品的绿色属性标签（如是否有“无塑包装”、“环保材料”等标识）。

输出：

1. 商品的绿色打分（绿色系数 × 绿色修正系数）。
2. 根据绿色属性修正系数后的奖励碳币数。
3. 奖励经验值（根据类型确定 2，5，3 ）
4. 商品名称

绿色系数：

- 本地生产：绿色系数 1.5
- 环保材料：绿色系数 1.3
- 无塑包装：绿色系数 2.0
- 可降解包装：绿色系数 1.8

绿色属性修正系数：

- 无塑包装：修正系数 1.5
- 可降解包装：修正系数 1.4

计算公式：

奖励碳币 = 类型基数 × 减碳系数 × 绿色属性修正系数

例如：

1. 如果用户购买本地生产的有机食品，奖励碳币 = 5× 1.5 × 1.5 = 12.25 碳币
2. 如果用户购买环保家电，奖励碳币 =10 × 2.0 × 1.4 = 28 碳币
